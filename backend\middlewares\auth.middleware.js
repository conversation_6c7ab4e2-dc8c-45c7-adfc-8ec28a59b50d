const { verifyToken } = require('../utils/jwt');
const { asyncHandler } = require('./error.middleware');

// Mock user for development
const mockUser = {
  _id: 'mock-user-id',
  id: 1,
  email: '<EMAIL>',
  fullName: 'Admin User',
  phone: '0123456789',
  address: 'Hà Nội, Việt Nam',
  role: 'admin',
  isActive: true,
  createdAt: new Date(),
  toJSON() {
    const { _id, ...user } = this;
    return { _id, ...user };
  }
};

// Protect routes - require authentication
const authMiddleware = asyncHandler(async (req, res, next) => {
  let token;

  // Check for token in headers
  if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
    try {
      // Get token from header
      token = req.headers.authorization.split(' ')[1];

      // Verify token
      const decoded = verifyToken(token);

      // For development, use mock user
      if (process.env.NODE_ENV === 'development') {
        req.user = mockUser;
        return next();
      }

      // In production, get user from database
      // const user = await User.findById(decoded.userId).select('-password');
      // if (!user) {
      //   return res.status(401).json({
      //     success: false,
      //     message: 'User not found'
      //   });
      // }
      // req.user = user;

      next();
    } catch (error) {
      console.error('Auth middleware error:', error.message);
      return res.status(401).json({
        success: false,
        message: 'Not authorized, token failed'
      });
    }
  }

  if (!token) {
    return res.status(401).json({
      success: false,
      message: 'Not authorized, no token'
    });
  }
});

// Optional auth - don't require authentication but set user if token exists
const optionalAuth = asyncHandler(async (req, res, next) => {
  let token;

  if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
    try {
      token = req.headers.authorization.split(' ')[1];
      const decoded = verifyToken(token);
      
      // For development, use mock user
      if (process.env.NODE_ENV === 'development') {
        req.user = mockUser;
      }
    } catch (error) {
      // Token invalid, but continue without user
      console.log('Optional auth - invalid token:', error.message);
    }
  }

  next();
});

module.exports = {
  authMiddleware,
  optionalAuth
};
