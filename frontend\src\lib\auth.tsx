import { createContext, useContext, useEffect, useState } from "react";
import { User, AuthState } from "@/types";
import { apiRequest } from "./queryClient";
import { useQueryClient } from "@tanstack/react-query";

interface AuthContextType extends AuthState {
  login: (email: string, password: string) => Promise<void>;
  register: (data: {
    email: string;
    password: string;
    fullName: string;
    confirmPassword: string;
  }) => Promise<void>;
  updateProfile: (data: {
    fullName: string;
    email: string;
    phone: string;
    address: string;
  }) => Promise<void>;
  logout: () => void;
  isLoading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const queryClient = useQueryClient();
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    token: null,
    isAuthenticated: false,
  });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const token = localStorage.getItem("authToken");
    const userData = localStorage.getItem("userData");

    console.log('Auth initialization:', { token: !!token, userData: !!userData });

    if (token && userData) {
      try {
        const user = JSON.parse(userData);
        console.log('Setting auth state:', { user: user.fullName, isAuthenticated: true });
        setAuthState({
          user,
          token,
          isAuthenticated: true,
        });
      } catch (error) {
        console.error("Error parsing user data:", error);
        localStorage.removeItem("authToken");
        localStorage.removeItem("userData");
      }
    } else {
      console.log('No auth data found, staying logged out');
    }
    setIsLoading(false);
  }, []);

  const login = async (email: string, password: string) => {
    try {
      console.log('Login attempt:', { email });

      const response = await apiRequest("POST", "/api/auth/login", {
        email,
        password,
      });

      if (!response.ok) {
        throw new Error('Login failed');
      }

      const result = await response.json();
      console.log('Login response:', { success: result.success, user: result.data?.user?.fullName });

      if (!result.success || !result.data?.token || !result.data?.user) {
        throw new Error(result.message || 'Invalid login response');
      }

      const { user, token } = result.data;

      // Clear cache when switching users
      queryClient.clear();

      localStorage.setItem("authToken", token);
      localStorage.setItem("userData", JSON.stringify(user));

      console.log('Setting auth state after login:', { user: user.fullName, isAuthenticated: true });

      setAuthState({
        user,
        token,
        isAuthenticated: true,
      });

      console.log('Login completed successfully');
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  };

  const register = async (userData: {
    email: string;
    password: string;
    fullName: string;
    confirmPassword: string;
  }) => {
    try {
      const response = await apiRequest("POST", "/api/auth/register", userData);

      const data = await response.json();

      // Clear cache when registering new user
      queryClient.clear();

      localStorage.setItem("authToken", data.token);
      localStorage.setItem("userData", JSON.stringify(data.user));

      setAuthState({
        user: data.user,
        token: data.token,
        isAuthenticated: true,
      });

      // Force page refresh to ensure clean state
      setTimeout(() => {
        window.location.reload();
      }, 100);
    } catch (error) {
      throw error;
    }
  };

  const updateProfile = async (profileData: {
    fullName: string;
    email: string;
    phone: string;
    address: string;
  }) => {
    try {
      const response = await apiRequest("PUT", "/api/auth/profile", profileData);
      const data = await response.json();

      // Update local storage and state
      localStorage.setItem("userData", JSON.stringify(data.user));
      setAuthState(prev => ({
        ...prev,
        user: data.user,
      }));

      // Clear cache to refresh user data
      queryClient.invalidateQueries({ queryKey: ["user"] });
    } catch (error) {
      throw error;
    }
  };

  const logout = () => {
    localStorage.removeItem("authToken");
    localStorage.removeItem("userData");

    // Clear all React Query cache
    console.log('Logout - Clearing orders cache');
    queryClient.clear();
    console.log('Logout - Cache cleared');

    setAuthState({
      user: null,
      token: null,
      isAuthenticated: false,
    });

    // Force page refresh to ensure clean state
    setTimeout(() => {
      window.location.reload();
    }, 100);
  };

  return (
    <AuthContext.Provider
      value={{
        ...authState,
        login,
        register,
        updateProfile,
        logout,
        isLoading,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}
