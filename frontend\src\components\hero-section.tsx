"use client";

import { But<PERSON> } from "@/components/ui/button";
import { useQuery } from "@tanstack/react-query";
import { Banner } from "@shared/schema";
import { useState, useEffect } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";

export default function HeroSection() {
  const [currentBannerIndex, setCurrentBannerIndex] = useState(0);

  const { data: bannersData } = useQuery({
    queryKey: ["/api/banners"],
    queryFn: async (): Promise<Banner[]> => {
      const response = await fetch("/api/banners/active");
      if (!response.ok) throw new Error('Failed to fetch banners');
      const result = await response.json();

      // Handle backend response structure: {success: true, data: {banners: [...]}}
      if (result.success && result.data && Array.isArray(result.data.banners)) {
        return result.data.banners;
      }

      // Fallback for direct array response
      return Array.isArray(result) ? result : [];
    },
    staleTime: 30 * 60 * 1000, // 30 minutes for banners
    gcTime: 60 * 60 * 1000, // 1 hour cache
    refetchOnWindowFocus: false,
    refetchOnMount: false, // Never refetch on mount
    refetchInterval: false,
    refetchOnReconnect: false,
    enabled: true, // Enabled to test with real database
  });

  // Ensure banners is always an array
  const banners: Banner[] = Array.isArray(bannersData) ? bannersData : [];

  // Auto slide
  useEffect(() => {
    if (banners.length > 1) {
      const interval = setInterval(() => {
        setCurrentBannerIndex((prev) => (prev + 1) % banners.length);
      }, 5000);
      return () => clearInterval(interval);
    }
  }, [banners.length]);

  const handleOrderNow = () => {
    document.getElementById("menu")?.scrollIntoView({ behavior: "smooth" });
  };

  const nextBanner = () => {
    setCurrentBannerIndex((prev) => (prev + 1) % banners.length);
  };

  const prevBanner = () => {
    setCurrentBannerIndex((prev) => (prev - 1 + banners.length) % banners.length);
  };

  const mainBanner = banners[currentBannerIndex] || banners[0];

  return (
    <section className="relative w-full h-[500px] bg-gradient-to-br from-primary via-red-600 to-orange-600 overflow-hidden">
      {/* Overlay */}
      <div className="absolute inset-0 bg-gradient-to-r from-black/60 via-black/40 to-black/30"></div>

      {/* Background Image - Full Width */}
      <div
        className="absolute inset-0 transition-all duration-1000 ease-in-out"
        style={{
          backgroundImage: mainBanner?.image
            ? `url(${mainBanner.image})`
            : "url('https://images.unsplash.com/photo-1504674900247-0877df9cc836?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&h=800')",
          backgroundSize: "cover",
          backgroundPosition: "center center",
          backgroundRepeat: "no-repeat",
        }}
      />

      {/* Content */}
      <div className="relative z-10 flex items-center justify-center h-full">
        <div className="text-center text-white px-4 max-w-4xl mx-auto">
          <h1 className="text-4xl md:text-6xl font-bold mb-4 leading-tight">
            {mainBanner?.title || "Delicious Food Delivered"}
          </h1>
          <p className="text-lg md:text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            {mainBanner?.description || "Fresh ingredients, authentic flavors, delivered right to your door"}
          </p>
          <Button
            onClick={handleOrderNow}
            size="lg"
            className="bg-primary hover:bg-primary/90 text-white px-8 py-3 text-lg font-semibold rounded-full shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200"
          >
            Order Now
          </Button>
        </div>
      </div>

      {/* Navigation Arrows */}
      {banners.length > 1 && (
        <>
          <button
            onClick={prevBanner}
            className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/30 backdrop-blur-sm hover:bg-white/50 text-white p-2 rounded-full z-20"
          >
            <ChevronLeft className="h-5 w-5" />
          </button>
          <button
            onClick={nextBanner}
            className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/30 backdrop-blur-sm hover:bg-white/50 text-white p-2 rounded-full z-20"
          >
            <ChevronRight className="h-5 w-5" />
          </button>
        </>
      )}

      {/* Indicators */}
      {banners.length > 1 && (
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2 z-20">
          {banners.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentBannerIndex(index)}
              className={`w-2.5 h-2.5 rounded-full ${
                index === currentBannerIndex
                  ? "bg-white scale-110"
                  : "bg-white/50 hover:bg-white/75"
              } transition-all`}
            />
          ))}
        </div>
      )}
    </section>
  );
}
