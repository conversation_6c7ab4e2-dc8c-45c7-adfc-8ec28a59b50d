import { useState } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useLocation } from "wouter";
import { Product } from "@shared/schema";
import Navigation from "@/components/navigation";
import HeroSection from "@/components/hero-section";
import ProductCard from "@/components/product-card";
import CartModal from "@/components/cart-modal";
import Footer from "@/components/footer";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";

export default function Home() {
  const [location] = useLocation();
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [searchQuery, setSearchQuery] = useState("");
  const queryClient = useQueryClient();

  // Parse URL search params
  const urlParams = new URLSearchParams(location.split('?')[1] || '');
  const initialSearch = urlParams.get('search') || '';

  const { data: productsData, isLoading, error } = useQuery({
    queryKey: ["/api/products", selectedCategory !== "all" ? selectedCategory : undefined, searchQuery || initialSearch],
    queryFn: async (): Promise<Product[]> => {
      const params = new URLSearchParams();
      if (selectedCategory !== "all") params.append('category', selectedCategory);
      if (searchQuery || initialSearch) params.append('search', searchQuery || initialSearch);

      const response = await fetch(`/api/products?${params}`);
      if (!response.ok) throw new Error('Failed to fetch products');
      const result = await response.json();

      // Handle backend response structure: {success: true, data: {products: [...], pagination: {...}}}
      if (result.success && result.data && Array.isArray(result.data.products)) {
        return result.data.products;
      }

      // Fallback for direct array response
      return Array.isArray(result) ? result : [];
    },
    staleTime: 15 * 60 * 1000, // 15 minutes for products
    gcTime: 30 * 60 * 1000, // 30 minutes cache
    refetchOnWindowFocus: false,
    refetchOnMount: false, // Never refetch on mount
    refetchInterval: false,
    refetchOnReconnect: false,
    enabled: true, // Enabled to test with real database
  });

  // Ensure products is always an array with fallback
  const products: Product[] = Array.isArray(productsData) ? productsData : [];

  const categories = [
    { id: "all", name: "Tất cả" },
    { id: "Món chính", name: "Món chính" },
    { id: "Món tráng miệng", name: "Món tráng miệng" },
    { id: "Đồ uống", name: "Đồ uống" },
    { id: "Món chay", name: "Món chay" },
    { id: "Món nhẹ", name: "Món nhẹ" },
  ];

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // Search will be handled by the query refetch
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />
      <HeroSection />
      
      {/* Product Catalog */}
      <section id="menu" className="py-20 bg-white -mt-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Section Header */}
          <div className="text-center mb-8">
            <h2 className="text-3xl md:text-4xl font-nunito font-bold text-gray-900 mb-4">
              Thực đơn đặc biệt
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto font-medium">
              Khám phá các món ăn ngon được chế biến tươi mới hàng ngày
            </p>
          </div>

          {/* Search Bar - Mobile */}
          <div className="md:hidden mb-8">
            <form onSubmit={handleSearch} className="relative">
              <Input
                type="text"
                placeholder="Tìm món ăn..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            </form>
          </div>

          {/* Category Filter */}
          <div className="mb-8">
            <div className="flex flex-wrap justify-center gap-3 mb-8 max-w-4xl mx-auto">
              {categories.map((category) => (
                <Button
                  key={category.id}
                  variant={selectedCategory === category.id ? "default" : "outline"}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`px-8 py-3 rounded-2xl font-nunito font-semibold text-sm transition-all duration-300 transform hover:scale-105 ${
                    selectedCategory === category.id
                      ? "bg-gradient-to-r from-primary to-red-600 text-white shadow-lg border-0"
                      : "bg-white text-gray-700 hover:bg-gradient-to-r hover:from-red-50 hover:to-orange-50 hover:text-primary border-2 border-gray-200 hover:border-primary/30 shadow-md"
                  }`}
                >
                  {category.name}
                </Button>
              ))}
            </div>
          </div>

          {/* Product Grid */}
          {error ? (
            <div className="text-center py-12">
              <p className="text-red-500 text-lg">Lỗi khi tải sản phẩm. Vui lòng thử lại sau.</p>
              <button
                onClick={() => window.location.reload()}
                className="mt-4 bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
              >
                Tải lại
              </button>
            </div>
          ) : isLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
              {[...Array(8)].map((_, i) => (
                <div key={i} className="bg-white rounded-xl shadow-lg overflow-hidden animate-pulse">
                  <div className="w-full h-48 bg-gray-200"></div>
                  <div className="p-6">
                    <div className="h-6 bg-gray-200 rounded mb-2"></div>
                    <div className="h-4 bg-gray-200 rounded mb-4"></div>
                    <div className="h-8 bg-gray-200 rounded"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : !Array.isArray(products) || products.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-500 text-lg">Không tìm thấy sản phẩm nào</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
              {products.map((product: Product) => (
                <ProductCard key={product.id} product={product} />
              ))}
            </div>
          )}

          {/* Load More Button */}
          {Array.isArray(products) && products.length > 0 && (
            <div className="text-center mt-12">
              <Button variant="outline" className="px-8 py-3">
                Xem thêm món ăn
              </Button>
            </div>
          )}
        </div>
      </section>

      {/* About Section */}
      <section id="about" className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-poppins font-bold text-gray-900 mb-4">
            Về Na Food
          </h2>
          <p className="text-gray-600 max-w-3xl mx-auto text-lg">
            Na Food là nền tảng đặt món ăn trực tuyến hàng đầu, mang đến cho bạn những trải nghiệm 
            ẩm thực tuyệt vời với đa dạng món ăn từ khắp nơi. Chúng tôi cam kết về chất lượng món ăn 
            và dịch vụ giao hàng nhanh chóng, đảm bảo sự hài lòng của khách hàng.
          </p>
        </div>
      </section>

      <Footer />
      <CartModal />
    </div>
  );
}
