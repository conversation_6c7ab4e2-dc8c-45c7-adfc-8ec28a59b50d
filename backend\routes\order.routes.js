const express = require('express');
const router = express.Router();

// Mock orders data
const mockOrders = [
  {
    _id: '687a747b4a75c3329d713b90',
    id: 1,
    orderNumber: 'NF12345678',
    customerInfo: {
      name: '<PERSON><PERSON><PERSON><PERSON>',
      email: 'ng<PERSON><PERSON><PERSON>@example.com',
      phone: '0123456789',
      address: '123 Đường ABC, Quận 1, TP.HCM'
    },
    items: [
      {
        productId: 1,
        name: 'Phở Bò',
        price: 85000,
        quantity: 2,
        image: '/images/pho-bo.jpg'
      },
      {
        productId: 5,
        name: '<PERSON>r<PERSON>',
        price: 35000,
        quantity: 1,
        image: '/images/tra-sua.jpg'
      }
    ],
    subtotal: 205000,
    deliveryFee: 25000,
    discount: 0,
    total: 230000,
    status: 'confirmed',
    paymentMethod: 'cash',
    paymentStatus: 'pending',
    deliveryType: 'delivery',
    estimatedDeliveryTime: new Date(Date.now() + 45 * 60 * 1000), // 45 minutes from now
    notes: 'Giao hàng nhanh',
    createdAt: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
    statusHistory: [
      {
        status: 'pending',
        timestamp: new Date(Date.now() - 30 * 60 * 1000),
        notes: 'Đơn hàng được tạo'
      },
      {
        status: 'confirmed',
        timestamp: new Date(Date.now() - 25 * 60 * 1000),
        notes: 'Đơn hàng đã được xác nhận'
      }
    ]
  },
  {
    _id: '687a747b4a75c3329d713b91',
    id: 2,
    orderNumber: 'NF12345679',
    customerInfo: {
      name: 'Trần Thị B',
      email: '<EMAIL>',
      phone: '0987654321',
      address: '456 Đường XYZ, Quận 2, TP.HCM'
    },
    items: [
      {
        productId: 2,
        name: 'Bún Chả',
        price: 75000,
        quantity: 1,
        image: '/images/bun-cha.jpg'
      }
    ],
    subtotal: 75000,
    deliveryFee: 20000,
    discount: 15000,
    total: 80000,
    status: 'delivered',
    paymentMethod: 'online',
    paymentStatus: 'paid',
    deliveryType: 'delivery',
    actualDeliveryTime: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
    rating: 5,
    review: 'Món ăn rất ngon, giao hàng nhanh!',
    createdAt: new Date(Date.now() - 3 * 60 * 60 * 1000), // 3 hours ago
    statusHistory: [
      {
        status: 'pending',
        timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000)
      },
      {
        status: 'confirmed',
        timestamp: new Date(Date.now() - 2.5 * 60 * 60 * 1000)
      },
      {
        status: 'preparing',
        timestamp: new Date(Date.now() - 2.3 * 60 * 60 * 1000)
      },
      {
        status: 'delivering',
        timestamp: new Date(Date.now() - 2.1 * 60 * 60 * 1000)
      },
      {
        status: 'delivered',
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000)
      }
    ]
  }
];

// POST /api/orders - Create new order (public with optional auth)
router.post('/', (req, res) => {
  try {
    const {
      customerInfo,
      items,
      deliveryType = 'delivery',
      paymentMethod = 'cash',
      notes
    } = req.body;

    // Validate required fields
    if (!customerInfo || !customerInfo.name || !customerInfo.email || !customerInfo.phone) {
      return res.status(400).json({
        success: false,
        message: 'Thông tin khách hàng không đầy đủ'
      });
    }

    if (!items || items.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Đơn hàng phải có ít nhất 1 sản phẩm'
      });
    }

    if (deliveryType === 'delivery' && !customerInfo.address) {
      return res.status(400).json({
        success: false,
        message: 'Địa chỉ giao hàng là bắt buộc'
      });
    }

    // Calculate totals
    const subtotal = items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    const deliveryFee = deliveryType === 'delivery' ? (subtotal >= 200000 ? 0 : 25000) : 0;
    const discount = 0; // Could implement discount logic here
    const total = subtotal + deliveryFee - discount;

    // Create new order
    const newOrder = {
      _id: `687a747b4a75c3329d713b${90 + mockOrders.length + 1}`,
      id: mockOrders.length + 1,
      orderNumber: `NF${Date.now().toString().slice(-8)}`,
      userId: req.user?.id || null,
      customerInfo,
      items,
      subtotal,
      deliveryFee,
      discount,
      total,
      status: 'pending',
      paymentMethod,
      paymentStatus: paymentMethod === 'cash' ? 'pending' : 'pending',
      deliveryType,
      estimatedDeliveryTime: new Date(Date.now() + 45 * 60 * 1000), // 45 minutes
      notes,
      createdAt: new Date(),
      statusHistory: [
        {
          status: 'pending',
          timestamp: new Date(),
          notes: 'Đơn hàng được tạo'
        }
      ]
    };

    // Add to mock data (in real app, save to database)
    mockOrders.push(newOrder);

    res.status(201).json({
      success: true,
      message: 'Đặt hàng thành công',
      data: {
        order: newOrder
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Lỗi server khi tạo đơn hàng',
      error: error.message
    });
  }
});

// GET /api/orders/statistics - Get order statistics (admin only)
router.get('/statistics', (req, res) => {
  try {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const todayOrders = mockOrders.filter(order => 
      new Date(order.createdAt) >= today
    );

    const statistics = {
      total: {
        orders: mockOrders.length,
        revenue: mockOrders.reduce((sum, order) => sum + order.total, 0),
        averageOrderValue: mockOrders.length > 0 ? 
          Math.round(mockOrders.reduce((sum, order) => sum + order.total, 0) / mockOrders.length) : 0
      },
      today: {
        orders: todayOrders.length,
        revenue: todayOrders.reduce((sum, order) => sum + order.total, 0)
      },
      byStatus: {
        pending: mockOrders.filter(o => o.status === 'pending').length,
        confirmed: mockOrders.filter(o => o.status === 'confirmed').length,
        preparing: mockOrders.filter(o => o.status === 'preparing').length,
        ready: mockOrders.filter(o => o.status === 'ready').length,
        delivering: mockOrders.filter(o => o.status === 'delivering').length,
        delivered: mockOrders.filter(o => o.status === 'delivered').length,
        cancelled: mockOrders.filter(o => o.status === 'cancelled').length
      },
      byPaymentMethod: {
        cash: mockOrders.filter(o => o.paymentMethod === 'cash').length,
        online: mockOrders.filter(o => o.paymentMethod === 'online').length,
        card: mockOrders.filter(o => o.paymentMethod === 'card').length
      }
    };

    res.json({
      success: true,
      data: statistics
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Lỗi server khi lấy thống kê đơn hàng',
      error: error.message
    });
  }
});

// GET /api/orders/me - Get current user's orders (protected)
router.get('/me', (req, res) => {
  try {
    const userId = req.user?.id;
    const userOrders = mockOrders.filter(order => order.userId === userId);

    res.json({
      success: true,
      data: {
        orders: userOrders.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Lỗi server khi lấy đơn hàng',
      error: error.message
    });
  }
});

// GET /api/orders - Get all orders (admin only)
router.get('/', (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 10, 
      status, 
      paymentStatus,
      startDate,
      endDate 
    } = req.query;

    let filteredOrders = [...mockOrders];

    // Filter by status
    if (status) {
      filteredOrders = filteredOrders.filter(order => order.status === status);
    }

    // Filter by payment status
    if (paymentStatus) {
      filteredOrders = filteredOrders.filter(order => order.paymentStatus === paymentStatus);
    }

    // Filter by date range
    if (startDate) {
      filteredOrders = filteredOrders.filter(order => 
        new Date(order.createdAt) >= new Date(startDate)
      );
    }
    if (endDate) {
      filteredOrders = filteredOrders.filter(order => 
        new Date(order.createdAt) <= new Date(endDate)
      );
    }

    // Sort by creation date (newest first)
    filteredOrders.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

    // Pagination
    const startIndex = (parseInt(page) - 1) * parseInt(limit);
    const endIndex = startIndex + parseInt(limit);
    const paginatedOrders = filteredOrders.slice(startIndex, endIndex);

    res.json({
      success: true,
      data: {
        orders: paginatedOrders,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(filteredOrders.length / parseInt(limit)),
          totalItems: filteredOrders.length,
          itemsPerPage: parseInt(limit)
        }
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Lỗi server khi lấy danh sách đơn hàng',
      error: error.message
    });
  }
});

// GET /api/orders/:id - Get specific order by ID (protected)
router.get('/:id', (req, res) => {
  try {
    const { id } = req.params;
    const order = mockOrders.find(o => 
      o.id.toString() === id || o._id === id || o.orderNumber === id
    );

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Đơn hàng không tồn tại'
      });
    }

    // Check if user can access this order
    if (req.user && req.user.role !== 'admin' && req.user.role !== 'staff') {
      if (order.userId !== req.user.id) {
        return res.status(403).json({
          success: false,
          message: 'Bạn không có quyền xem đơn hàng này'
        });
      }
    }

    res.json({
      success: true,
      data: {
        order
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Lỗi server khi lấy thông tin đơn hàng',
      error: error.message
    });
  }
});

module.exports = router;
