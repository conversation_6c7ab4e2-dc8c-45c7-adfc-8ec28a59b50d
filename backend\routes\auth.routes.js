const express = require('express');
const router = express.Router();

// Import controllers
const {
  register,
  login,
  getProfile,
  updateProfile,
  changePassword,
  refreshToken,
  logout
} = require('../controllers/auth.controller');

// Import middleware
const { authMiddleware } = require('../middlewares/auth.middleware');

// Import validators
const {
  registerValidation,
  loginValidation,
  updateProfileValidation,
  changePasswordValidation,
  handleValidationErrors
} = require('../validators/auth.validator');

// RESTful API Routes for Authentication
// Following REST conventions: HTTP Method + Resource + Action

// POST /api/auth/register - User registration (public)
router.post('/register', registerValidation, handleValidationErrors, register);

// POST /api/auth/login - User login (public)
router.post('/login', loginValidation, handleValidationErrors, login);

// POST /api/auth/refresh - Refresh access token (public)
router.post('/refresh', refreshToken);

// POST /api/auth/logout - User logout (protected)
router.post('/logout', authMiddleware, logout);

// GET /api/auth/profile - Get current user profile (protected)
router.get('/profile', authMiddleware, getProfile);

// PUT /api/auth/profile - Update current user profile (protected)
router.put('/profile', authMiddleware, updateProfileValidation, handleValidationErrors, updateProfile);

// PATCH /api/auth/profile - Partial update current user profile (protected)
router.patch('/profile', authMiddleware, updateProfileValidation, handleValidationErrors, updateProfile);

// PUT /api/auth/password - Change password (protected)
router.put('/password', authMiddleware, changePasswordValidation, handleValidationErrors, changePassword);

module.exports = router;
