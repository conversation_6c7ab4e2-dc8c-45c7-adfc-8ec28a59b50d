import { useState, useEffect } from "react";
import { useAuth } from "@/lib/auth.tsx";
import { useLocation } from "wouter";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import Navigation from "@/components/navigation";
import Dashboard from "@/components/admin/dashboard";
import ProductManagement from "@/components/admin/product-management";
import OrderManagement from "@/components/admin/order-management";
import UserManagement from "@/components/admin/user-management";
import ReviewManagement from "@/components/admin/review-management";
import BannerManagement from "@/components/admin/banner-management";
import Statistics from "@/components/admin/statistics";
import { 
  BarChart3, 
  Package, 
  ShoppingCart, 
  Users, 
  Star, 
  Image,
  TrendingUp
} from "lucide-react";

export default function Admin() {
  const { user, isAuthenticated } = useAuth();
  const [, setLocation] = useLocation();
  const [activeTab, setActiveTab] = useState("dashboard");

  useEffect(() => {
    if (!isAuthenticated || user?.role !== 'admin') {
      setLocation('/auth');
    }
  }, [isAuthenticated, user?.role, setLocation]);

  if (!isAuthenticated || user?.role !== 'admin') {
    return null;
  }

  const navItems = [
    { id: "dashboard", label: "Tổng quan", icon: BarChart3 },
    { id: "products", label: "Quản lý món ăn", icon: Package },
    { id: "orders", label: "Đơn hàng", icon: ShoppingCart },
    { id: "users", label: "Người dùng", icon: Users },
    { id: "reviews", label: "Đánh giá", icon: Star },
    { id: "banners", label: "Banner", icon: Image },
    { id: "statistics", label: "Thống kê", icon: TrendingUp },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      <Navigation />

      <div className="flex">
        {/* Sidebar */}
        <div className="w-72 bg-white shadow-xl min-h-screen border-r border-gray-200">
          <div className="p-6 border-b border-gray-200 bg-gradient-to-r from-primary to-red-600">
            <h2 className="text-xl font-bold text-white font-nunito">Quản trị viên</h2>
            <p className="text-sm text-red-100 font-medium">Na Food Admin Panel</p>
            <div className="mt-3 flex items-center">
              <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center">
                <span className="text-primary font-bold text-sm">👨‍💼</span>
              </div>
              <span className="ml-2 text-white font-medium">{user?.fullName}</span>
            </div>
          </div>
          
          <nav className="mt-8 px-4">
            {navItems.map((item) => {
              const Icon = item.icon;
              return (
                <button
                  key={item.id}
                  onClick={() => setActiveTab(item.id)}
                  className={`flex items-center w-full px-4 py-4 mb-2 text-left transition-all duration-300 rounded-xl font-nunito font-medium ${
                    activeTab === item.id
                      ? "text-white bg-gradient-to-r from-primary to-red-600 shadow-lg transform scale-105 border-l-4 border-red-800"
                      : "text-gray-700 hover:text-primary hover:bg-gradient-to-r hover:from-red-50 hover:to-orange-50 hover:shadow-md hover:transform hover:scale-102"
                  }`}
                >
                  <Icon className={`h-5 w-5 mr-4 ${activeTab === item.id ? 'text-white' : 'text-gray-500'}`} />
                  <span className="text-sm">{item.label}</span>
                  {activeTab === item.id && (
                    <div className="ml-auto w-2 h-2 bg-white rounded-full"></div>
                  )}
                </button>
              );
            })}
          </nav>
        </div>

        {/* Main Content */}
        <div className="flex-1 pt-3 px-4 pb-4 bg-gradient-to-br from-gray-50 to-white">
          <div className="max-w-7xl mx-auto">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsContent value="dashboard" className="admin-card rounded-xl p-5">
                <Dashboard />
              </TabsContent>

              <TabsContent value="products" className="admin-card rounded-xl p-5">
                <ProductManagement />
              </TabsContent>

              <TabsContent value="orders" className="admin-card rounded-xl p-5">
                <OrderManagement />
              </TabsContent>

              <TabsContent value="users" className="admin-card rounded-xl p-5">
                <UserManagement />
              </TabsContent>

              <TabsContent value="reviews" className="admin-card rounded-xl p-5">
                <ReviewManagement />
              </TabsContent>

              <TabsContent value="banners" className="admin-card rounded-xl p-5">
                <BannerManagement />
              </TabsContent>

              <TabsContent value="statistics" className="admin-card rounded-xl p-5">
                <Statistics />
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </div>
  );
}
