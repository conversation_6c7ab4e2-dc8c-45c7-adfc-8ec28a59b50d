const express = require('express');
const router = express.Router();

// Mock products data
const mockProducts = [
  {
    _id: '687a747b4a75c3329d713b80',
    id: 1,
    name: 'Phở B<PERSON>',
    price: 85000,
    originalPrice: 95000,
    category: '<PERSON><PERSON> chính',
    description: 'Phở bò truyền thống Hà <PERSON>ội với nước dùng đậm đà, thịt bò tươi ngon',
    image: '/images/pho-bo.jpg',
    isActive: true,
    isFeatured: true,
    rating: 4.5,
    reviewCount: 25,
    discount: 10,
    tags: ['Phở', '<PERSON><PERSON> truyền thống', '<PERSON>óng hổi'],
    preparationTime: 15
  },
  {
    _id: '687a747b4a75c3329d713b81',
    id: 2,
    name: '<PERSON><PERSON>',
    price: 75000,
    category: 'M<PERSON> chính',
    description: '<PERSON><PERSON> chả Hà Nội đặc biệt với thịt nướng thơm lừng',
    image: '/images/bun-cha.jpg',
    isActive: true,
    isFeatured: true,
    rating: 4.7,
    reviewCount: 18,
    tags: ['Bún', 'Nướng', 'Đặc sản <PERSON>'],
    preparationTime: 20
  },
  {
    _id: '687a747b4a75c3329d713b82',
    id: 3,
    name: 'Bánh Mì',
    price: 25000,
    category: 'Món nhẹ',
    description: 'Bánh mì thịt nướng với rau sống tươi ngon',
    image: '/images/banh-mi.jpg',
    isActive: true,
    rating: 4.3,
    reviewCount: 32,
    tags: ['Bánh mì', 'Nhanh gọn', 'Tiện lợi'],
    preparationTime: 5
  },
  {
    _id: '687a747b4a75c3329d713b83',
    id: 4,
    name: 'Cơm Tấm',
    price: 65000,
    category: 'Món chính',
    description: 'Cơm tấm sườn nướng đặc biệt với chả trứng',
    image: '/images/com-tam.jpg',
    isActive: true,
    rating: 4.4,
    reviewCount: 15,
    tags: ['Cơm', 'Sườn nướng', 'Đặc biệt'],
    preparationTime: 18
  },
  {
    _id: '687a747b4a75c3329d713b84',
    id: 5,
    name: 'Trà Sữa',
    price: 35000,
    category: 'Đồ uống',
    description: 'Trà sữa trân châu đường đen thơm ngon',
    image: '/images/tra-sua.jpg',
    isActive: true,
    rating: 4.2,
    reviewCount: 28,
    tags: ['Trà sữa', 'Trân châu', 'Mát lạnh'],
    preparationTime: 8
  },
  {
    _id: '687a747b4a75c3329d713b85',
    id: 6,
    name: 'Chè Ba Màu',
    price: 30000,
    category: 'Tráng miệng',
    description: 'Chè ba màu truyền thống với đậu xanh, đậu đỏ và thạch',
    image: '/images/che-ba-mau.jpg',
    isActive: true,
    rating: 4.1,
    reviewCount: 12,
    tags: ['Chè', 'Tráng miệng', 'Mát lạnh'],
    preparationTime: 10
  }
];

// GET /api/products - Get all products (with pagination, filtering, search)
router.get('/', (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 12, 
      category, 
      search, 
      featured, 
      minPrice, 
      maxPrice,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    let filteredProducts = [...mockProducts];

    // Filter by category
    if (category && category !== 'all') {
      filteredProducts = filteredProducts.filter(product => 
        product.category.toLowerCase() === category.toLowerCase()
      );
    }

    // Filter by search term
    if (search) {
      const searchTerm = search.toLowerCase();
      filteredProducts = filteredProducts.filter(product =>
        product.name.toLowerCase().includes(searchTerm) ||
        product.description.toLowerCase().includes(searchTerm) ||
        product.tags.some(tag => tag.toLowerCase().includes(searchTerm))
      );
    }

    // Filter by featured
    if (featured === 'true') {
      filteredProducts = filteredProducts.filter(product => product.isFeatured);
    }

    // Filter by price range
    if (minPrice) {
      filteredProducts = filteredProducts.filter(product => product.price >= parseInt(minPrice));
    }
    if (maxPrice) {
      filteredProducts = filteredProducts.filter(product => product.price <= parseInt(maxPrice));
    }

    // Sort products
    filteredProducts.sort((a, b) => {
      let aValue = a[sortBy];
      let bValue = b[sortBy];
      
      if (sortBy === 'price' || sortBy === 'rating') {
        aValue = parseFloat(aValue);
        bValue = parseFloat(bValue);
      }
      
      if (sortOrder === 'desc') {
        return bValue > aValue ? 1 : -1;
      } else {
        return aValue > bValue ? 1 : -1;
      }
    });

    // Pagination
    const startIndex = (parseInt(page) - 1) * parseInt(limit);
    const endIndex = startIndex + parseInt(limit);
    const paginatedProducts = filteredProducts.slice(startIndex, endIndex);

    res.json({
      success: true,
      data: {
        products: paginatedProducts,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(filteredProducts.length / parseInt(limit)),
          totalItems: filteredProducts.length,
          itemsPerPage: parseInt(limit),
          hasNextPage: endIndex < filteredProducts.length,
          hasPrevPage: startIndex > 0
        },
        filters: {
          category,
          search,
          featured,
          minPrice,
          maxPrice,
          sortBy,
          sortOrder
        }
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Lỗi server khi lấy danh sách sản phẩm',
      error: error.message
    });
  }
});

// GET /api/products/categories - Get product categories
router.get('/categories', (req, res) => {
  const categories = [...new Set(mockProducts.map(product => product.category))];
  
  res.json({
    success: true,
    data: {
      categories: categories.map(category => ({
        name: category,
        count: mockProducts.filter(product => product.category === category).length
      }))
    }
  });
});

// GET /api/products/featured - Get featured products
router.get('/featured', (req, res) => {
  const featuredProducts = mockProducts.filter(product => product.isFeatured);
  
  res.json({
    success: true,
    data: {
      products: featuredProducts
    }
  });
});

// GET /api/products/search - Search products (alternative to query params)
router.get('/search', (req, res) => {
  const { q: searchTerm } = req.query;
  
  if (!searchTerm) {
    return res.status(400).json({
      success: false,
      message: 'Search term is required'
    });
  }

  const searchResults = mockProducts.filter(product =>
    product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    product.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    product.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  res.json({
    success: true,
    data: {
      products: searchResults,
      searchTerm,
      count: searchResults.length
    }
  });
});

// GET /api/products/:id - Get specific product by ID
router.get('/:id', (req, res) => {
  const { id } = req.params;
  
  const product = mockProducts.find(p => 
    p.id.toString() === id || p._id === id
  );

  if (!product) {
    return res.status(404).json({
      success: false,
      message: 'Sản phẩm không tồn tại'
    });
  }

  res.json({
    success: true,
    data: {
      product
    }
  });
});

module.exports = router;
