const { body, validationResult } = require('express-validator');

// Validation rules for user registration
const registerValidation = [
  body('username')
    .trim()
    .isLength({ min: 3, max: 50 })
    .withMessage('Username phải có từ 3-50 ký tự')
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('Username chỉ được chứa chữ cái, số và dấu gạch dưới'),
  
  body('email')
    .isEmail()
    .withMessage('Email không hợp lệ')
    .normalizeEmail(),
  
  body('password')
    .isLength({ min: 3 })
    .withMessage('Mật khẩu phải có ít nhất 3 ký tự'),
  
  body('fullName')
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Tên đầy đủ là bắt buộc và không quá 100 ký tự'),
  
  body('phone')
    .optional()
    .isLength({ min: 0, max: 20 })
    .withMessage('<PERSON><PERSON> điện thoại không hợp lệ'),
  
  body('address')
    .optional()
    .isLength({ max: 200 })
    .withMessage('Địa chỉ không được quá 200 ký tự')
];

// Validation rules for user login
const loginValidation = [
  body('email')
    .isEmail()
    .withMessage('Email không hợp lệ')
    .normalizeEmail(),
  
  body('password')
    .notEmpty()
    .withMessage('Mật khẩu là bắt buộc')
];

// Validation rules for password change
const changePasswordValidation = [
  body('currentPassword')
    .notEmpty()
    .withMessage('Mật khẩu hiện tại là bắt buộc'),
  
  body('newPassword')
    .isLength({ min: 3 })
    .withMessage('Mật khẩu mới phải có ít nhất 3 ký tự'),
  
  body('confirmPassword')
    .custom((value, { req }) => {
      if (value !== req.body.newPassword) {
        throw new Error('Xác nhận mật khẩu không khớp');
      }
      return true;
    })
];

// Validation rules for profile update
const updateProfileValidation = [
  body('fullName')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Tên đầy đủ phải có từ 1-100 ký tự'),
  
  body('phone')
    .optional()
    .isLength({ max: 20 })
    .withMessage('Số điện thoại không hợp lệ'),
  
  body('address')
    .optional()
    .isLength({ max: 200 })
    .withMessage('Địa chỉ không được quá 200 ký tự')
];

// Validation rules for forgot password
const forgotPasswordValidation = [
  body('email')
    .isEmail()
    .withMessage('Email không hợp lệ')
    .normalizeEmail()
];

// Validation rules for reset password
const resetPasswordValidation = [
  body('token')
    .notEmpty()
    .withMessage('Token là bắt buộc'),
  
  body('newPassword')
    .isLength({ min: 3 })
    .withMessage('Mật khẩu mới phải có ít nhất 3 ký tự'),
  
  body('confirmPassword')
    .custom((value, { req }) => {
      if (value !== req.body.newPassword) {
        throw new Error('Xác nhận mật khẩu không khớp');
      }
      return true;
    })
];

// Handle validation errors
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation errors',
      errors: errors.array()
    });
  }
  
  next();
};

module.exports = {
  registerValidation,
  loginValidation,
  changePasswordValidation,
  updateProfileValidation,
  forgotPasswordValidation,
  resetPasswordValidation,
  handleValidationErrors
};
