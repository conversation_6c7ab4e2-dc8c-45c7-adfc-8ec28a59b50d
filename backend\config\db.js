const mongoose = require('mongoose');

const connectDB = async () => {
  try {
    const mongoURI = process.env.DATABASE_URL || process.env.MONGODB_URI;
    
    if (!mongoURI) {
      throw new Error('MongoDB connection string not found in environment variables');
    }

    console.log('🔄 Connecting to MongoDB...');
    
    const conn = await mongoose.connect(mongoURI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });

    console.log('📦 Mongoose connected to MongoDB');
    console.log('✅ MongoDB connected successfully');
    console.log(`📍 Database: ${conn.connection.name}`);
    
    return conn;
  } catch (error) {
    console.error('❌ MongoDB connection error:', error.message);
    throw error;
  }
};

const disconnectDB = async () => {
  try {
    await mongoose.disconnect();
    console.log('🔌 MongoDB disconnected');
  } catch (error) {
    console.error('❌ MongoDB disconnect error:', error.message);
  }
};

module.exports = {
  connectDB,
  disconnectDB
};
