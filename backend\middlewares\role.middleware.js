// Check if user is admin
const adminOnly = (req, res, next) => {
  if (req.user && req.user.role === 'admin') {
    next();
  } else {
    res.status(403).json({
      success: false,
      message: 'Access denied. Admin role required.'
    });
  }
};

// Check if user is staff or admin
const staffAndAdmin = (req, res, next) => {
  if (req.user && (req.user.role === 'staff' || req.user.role === 'admin')) {
    next();
  } else {
    res.status(403).json({
      success: false,
      message: 'Access denied. Staff or Admin role required.'
    });
  }
};

// Check if user is the owner of the resource or admin
const ownerOrAdmin = (req, res, next) => {
  if (req.user && (req.user.id.toString() === req.params.id || req.user.role === 'admin')) {
    next();
  } else {
    res.status(403).json({
      success: false,
      message: 'Access denied. You can only access your own resources.'
    });
  }
};

module.exports = {
  adminOnly,
  staffAndAdmin,
  ownerOrAdmin
};
