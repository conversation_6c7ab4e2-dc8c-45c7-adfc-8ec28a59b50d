const mongoose = require('mongoose');

const bannerSchema = new mongoose.Schema({
  id: {
    type: Number,
    unique: true,
    default: () => Date.now()
  },
  title: {
    type: String,
    required: [true, 'Banner title is required'],
    trim: true,
    maxlength: [100, 'Title cannot exceed 100 characters']
  },
  description: {
    type: String,
    trim: true,
    maxlength: [200, 'Description cannot exceed 200 characters']
  },
  image: {
    type: String,
    required: [true, 'Banner image is required']
  },
  link: {
    type: String,
    trim: true
  },
  isActive: {
    type: Boolean,
    default: true
  },
  order: {
    type: Number,
    default: 0
  },
  startDate: {
    type: Date,
    default: Date.now
  },
  endDate: {
    type: Date
  },
  clickCount: {
    type: Number,
    default: 0
  },
  impressions: {
    type: Number,
    default: 0
  },
  targetAudience: {
    type: String,
    enum: ['all', 'new_users', 'returning_users', 'premium_users'],
    default: 'all'
  },
  position: {
    type: String,
    enum: ['hero', 'sidebar', 'footer', 'popup'],
    default: 'hero'
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Indexes
bannerSchema.index({ isActive: 1, order: 1 });
bannerSchema.index({ startDate: 1, endDate: 1 });
bannerSchema.index({ position: 1 });

// Virtual for click-through rate
bannerSchema.virtual('ctr').get(function() {
  if (this.impressions === 0) return 0;
  return Math.round((this.clickCount / this.impressions) * 100 * 100) / 100;
});

// Check if banner is currently active
bannerSchema.virtual('isCurrentlyActive').get(function() {
  const now = new Date();
  const isWithinDateRange = (!this.startDate || this.startDate <= now) && 
                           (!this.endDate || this.endDate >= now);
  return this.isActive && isWithinDateRange;
});

// Increment click count
bannerSchema.methods.incrementClicks = async function() {
  this.clickCount += 1;
  await this.save();
};

// Increment impressions
bannerSchema.methods.incrementImpressions = async function() {
  this.impressions += 1;
  await this.save();
};

// Transform output
bannerSchema.set('toJSON', { virtuals: true });

module.exports = mongoose.model('Banner', bannerSchema);
