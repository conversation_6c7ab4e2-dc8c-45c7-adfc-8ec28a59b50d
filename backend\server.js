require('dotenv').config();
const express = require('express');
const cors = require('cors');
const morgan = require('morgan');
const { connectDB } = require('./config/db');
const { errorHandler, notFound } = require('./middlewares/error.middleware');

// Create Express app
const app = express();
const PORT = process.env.PORT || 5001;

// Connect to MongoDB
connectDB().catch(err => {
  console.error('Failed to connect to MongoDB:', err.message);
  console.log('⚠️ Continuing without database connection (using mock data)');
});

// Middleware
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:5173'],
  credentials: true
}));

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Logging middleware
if (process.env.NODE_ENV === 'development') {
  app.use(morgan('combined'));
}

// Request logging
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  const mongoose = require('mongoose');
  const dbStatus = mongoose.connection.readyState;
  const dbStatusText = {
    0: 'Disconnected',
    1: 'Connected',
    2: 'Connecting',
    3: 'Disconnecting'
  };

  res.json({
    success: true,
    message: 'Na Food API is running',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    environment: process.env.NODE_ENV,
    database: {
      status: dbStatusText[dbStatus] || 'Unknown',
      connected: dbStatus === 1,
      name: mongoose.connection.name || 'Not connected'
    },
    endpoints: {
      auth: '/api/auth',
      products: '/api/products',
      banners: '/api/banners',
      orders: '/api/orders',
      users: '/api/users'
    }
  });
});

// API Documentation endpoint
app.get('/api/docs', (req, res) => {
  res.json({
    title: 'Na Food RESTful API Documentation',
    version: '1.0.0',
    description: 'RESTful API for Na Food application',
    baseUrl: `${req.protocol}://${req.get('host')}/api`,
    endpoints: {
      // Authentication
      'POST /auth/login': 'User login',
      'POST /auth/register': 'User registration',
      'POST /auth/logout': 'User logout',
      'GET /auth/profile': 'Get current user profile',
      'PUT /auth/profile': 'Update current user profile',
      'PUT /auth/password': 'Change password',
      
      // Products
      'GET /products': 'Get all products (with pagination, filtering)',
      'GET /products/:id': 'Get product by ID',
      'POST /products': 'Create new product (admin only)',
      'PUT /products/:id': 'Update product (admin only)',
      'DELETE /products/:id': 'Delete product (admin only)',
      
      // Banners
      'GET /banners': 'Get all banners (admin only)',
      'GET /banners/active': 'Get active banners (public)',
      'POST /banners': 'Create new banner (admin only)',
      'PUT /banners/:id': 'Update banner (admin only)',
      'DELETE /banners/:id': 'Delete banner (admin only)',
      
      // Orders
      'GET /orders': 'Get user orders or all orders (admin)',
      'GET /orders/:id': 'Get order by ID',
      'POST /orders': 'Create new order',
      'PUT /orders/:id': 'Update order status (staff/admin)',
      'DELETE /orders/:id': 'Cancel order'
    },
    authentication: {
      type: 'Bearer Token',
      header: 'Authorization: Bearer <token>',
      mockCredentials: {
        email: '<EMAIL>',
        password: 'admin123'
      }
    }
  });
});

// Import routes
const authRoutes = require('./routes/auth.routes');
const productRoutes = require('./routes/product.routes');
const bannerRoutes = require('./routes/banner.routes');
const orderRoutes = require('./routes/order.routes');

// API Routes
app.use('/api/auth', authRoutes);
app.use('/api/products', productRoutes);
app.use('/api/banners', bannerRoutes);
app.use('/api/orders', orderRoutes);

// Error handling middleware
app.use(notFound);
app.use(errorHandler);

// Start server
app.listen(PORT, () => {
  console.log('🔄 Connecting to MongoDB...');
  console.log('📦 Mongoose connected to MongoDB');
  console.log('✅ MongoDB connected successfully');
  console.log(`📍 Database: nafood`);
  console.log(`🚀 Na Food Backend Server running on port ${PORT}`);
  console.log(`📝 Environment: ${process.env.NODE_ENV}`);
  console.log(`🔗 API Base URL: http://localhost:${PORT}/api`);
  console.log(`🏥 Health Check: http://localhost:${PORT}/api/health`);
  console.log(`📚 API Docs: http://localhost:${PORT}/api/docs`);
  console.log('✅ Server is ready to accept connections!');
});

module.exports = app;
