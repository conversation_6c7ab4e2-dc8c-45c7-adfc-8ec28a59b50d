const express = require('express');
const router = express.Router();

// Mock banners data
const mockBanners = [
  {
    _id: '687a747b4a75c3329d713b82',
    id: 1,
    title: '<PERSON><PERSON><PERSON><PERSON><PERSON> mãi đặc biệt',
    description: '<PERSON><PERSON><PERSON><PERSON> 20% cho đơn hàng đầu tiên',
    image: '/images/banner-1.jpg',
    link: '/products?featured=true',
    isActive: true,
    order: 1,
    position: 'hero',
    startDate: new Date('2024-01-01'),
    endDate: new Date('2024-12-31'),
    clickCount: 125,
    impressions: 1500
  },
  {
    _id: '687a747b4a75c3329d713b83',
    id: 2,
    title: '<PERSON><PERSON> mới hấp dẫn',
    description: 'Thử ngay các món ăn mới nhất của chúng tôi',
    image: '/images/banner-2.jpg',
    link: '/products?category=Món chính',
    isActive: true,
    order: 2,
    position: 'hero',
    startDate: new Date('2024-01-01'),
    endDate: new Date('2024-12-31'),
    clickCount: 89,
    impressions: 1200
  },
  {
    _id: '687a747b4a75c3329d713b84',
    id: 3,
    title: 'Giao hàng miễn phí',
    description: 'Miễn phí giao hàng cho đơn từ 200k',
    image: '/images/banner-3.jpg',
    link: '/products',
    isActive: true,
    order: 3,
    position: 'hero',
    startDate: new Date('2024-01-01'),
    endDate: new Date('2024-12-31'),
    clickCount: 67,
    impressions: 980
  }
];

// GET /api/banners/active - Get active banners (public)
router.get('/active', (req, res) => {
  try {
    const now = new Date();
    const activeBanners = mockBanners.filter(banner => {
      const isWithinDateRange = (!banner.startDate || banner.startDate <= now) && 
                               (!banner.endDate || banner.endDate >= now);
      return banner.isActive && isWithinDateRange;
    }).sort((a, b) => a.order - b.order);

    // Increment impressions (in real app, you might want to batch this)
    activeBanners.forEach(banner => {
      banner.impressions += 1;
    });

    res.json({
      success: true,
      data: {
        banners: activeBanners
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Lỗi server khi lấy danh sách banner',
      error: error.message
    });
  }
});

// POST /api/banners/:id/click - Track banner click (public)
router.post('/:id/click', (req, res) => {
  try {
    const { id } = req.params;
    const banner = mockBanners.find(b => 
      b.id.toString() === id || b._id === id
    );

    if (!banner) {
      return res.status(404).json({
        success: false,
        message: 'Banner không tồn tại'
      });
    }

    // Increment click count
    banner.clickCount += 1;

    res.json({
      success: true,
      message: 'Click tracked successfully',
      data: {
        bannerId: banner.id,
        clickCount: banner.clickCount,
        link: banner.link
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Lỗi server khi track click',
      error: error.message
    });
  }
});

// GET /api/banners/statistics - Get banner statistics (admin only)
router.get('/statistics', (req, res) => {
  try {
    const statistics = mockBanners.map(banner => ({
      id: banner.id,
      title: banner.title,
      impressions: banner.impressions,
      clickCount: banner.clickCount,
      ctr: banner.impressions > 0 ? Math.round((banner.clickCount / banner.impressions) * 100 * 100) / 100 : 0,
      isActive: banner.isActive
    }));

    const totalStats = {
      totalBanners: mockBanners.length,
      activeBanners: mockBanners.filter(b => b.isActive).length,
      totalImpressions: mockBanners.reduce((sum, b) => sum + b.impressions, 0),
      totalClicks: mockBanners.reduce((sum, b) => sum + b.clickCount, 0),
      averageCTR: 0
    };

    if (totalStats.totalImpressions > 0) {
      totalStats.averageCTR = Math.round((totalStats.totalClicks / totalStats.totalImpressions) * 100 * 100) / 100;
    }

    res.json({
      success: true,
      data: {
        banners: statistics,
        summary: totalStats
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Lỗi server khi lấy thống kê banner',
      error: error.message
    });
  }
});

// GET /api/banners - Get all banners (admin only)
router.get('/', (req, res) => {
  try {
    const { page = 1, limit = 10, status } = req.query;

    let filteredBanners = [...mockBanners];

    // Filter by status
    if (status === 'active') {
      filteredBanners = filteredBanners.filter(banner => banner.isActive);
    } else if (status === 'inactive') {
      filteredBanners = filteredBanners.filter(banner => !banner.isActive);
    }

    // Sort by order
    filteredBanners.sort((a, b) => a.order - b.order);

    // Pagination
    const startIndex = (parseInt(page) - 1) * parseInt(limit);
    const endIndex = startIndex + parseInt(limit);
    const paginatedBanners = filteredBanners.slice(startIndex, endIndex);

    res.json({
      success: true,
      data: {
        banners: paginatedBanners,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(filteredBanners.length / parseInt(limit)),
          totalItems: filteredBanners.length,
          itemsPerPage: parseInt(limit)
        }
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Lỗi server khi lấy danh sách banner',
      error: error.message
    });
  }
});

// GET /api/banners/:id - Get specific banner by ID (admin only)
router.get('/:id', (req, res) => {
  try {
    const { id } = req.params;
    const banner = mockBanners.find(b => 
      b.id.toString() === id || b._id === id
    );

    if (!banner) {
      return res.status(404).json({
        success: false,
        message: 'Banner không tồn tại'
      });
    }

    res.json({
      success: true,
      data: {
        banner
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Lỗi server khi lấy thông tin banner',
      error: error.message
    });
  }
});

module.exports = router;
